import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { HiUserGroup } from "react-icons/hi";
import { IoIosSend } from "react-icons/io";
import { GiCrossMark } from "react-icons/gi";
import { FaRegUser } from "react-icons/fa";
import { IoIosPersonAdd } from "react-icons/io";
import { IoMdCheckmarkCircle } from "react-icons/io";
import axiosInstance from "../config/axios";
import {
  initializeSocket,
  reccieveMessage,
  sendMessage,
} from "../config/socket";
import { useUser } from "../context/userContext";

const Project = () => {
  const location = useLocation();
  const [isSidePanelOpen, setIsSidePanelOpen] = useState(false);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [users, setUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [project, setProject] = useState(location.state.project);
  const [message, setMessage] = useState("");

  const messageBox = React.createRef();

  const { user } = useUser();

  useEffect(() => {
    initializeSocket(project._id);

    reccieveMessage("project-message", (data) => {
      console.log(data);
      appenIncommingMessage(data);
    });

    if (isUserModalOpen) {
      fetchUsers();
    }

    axiosInstance.get(`/api/user/${location.state.project._id}`).then((res) => {
      setProject(res.data.project);
    });
  }, [isUserModalOpen]);

  const send = () => {
    if (!message.trim()) return;

    const messageObj = {
      message,
      sender: user,
    };

    sendMessage("project-message", messageObj);
    appendOutgoingMessages(messageObj);

    setMessage("");
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get("/api/user/all");
      setUsers(response.data.users);
    } catch (error) {
      setError("Failed to fetch users");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const toggleUserSelection = (userId) => {
    setSelectedUsers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };

  const handleAddUsers = async () => {
    if (selectedUsers.length === 0) {
      setError("Please select at least one user");
      return;
    }

    try {
      setLoading(true);
      await axiosInstance.put("/api/project/add-user", {
        projectId: location.state.project._id,
        users: selectedUsers,
      });

      setIsUserModalOpen(false);
      setSelectedUsers([]);
    } catch (error) {
      setError(error.response?.data || "Failed to add users");
    } finally {
      setLoading(false);
    }
  };

  const appenIncommingMessage = (messageObj) => {
    const messageBox = document.querySelector(".message-box");
    messageBox.innerHTML += `<div className="incomming-messages flex flex-col p-2 bg-fuchsia-200 w-fit rounded-md max-w-56">
              <small className="opacity-65 text-xm">${messageObj.sender.email}</small>
              <p className="text-sm leading-4.5 pt-1 font-sans">${messageObj.message}</p>
            </div>`;

    messageBox.appendChild(messageBox.lastChild);
  };

  const appendOutgoingMessages = (messageObj) => {
    const messageBox = document.querySelector(".message-box");
    messageBox.innerHTML += `<div className="incomming-messages flex flex-col p-2 bg-fuchsia-200 w-fit rounded-md max-w-56">>
              <small className="opacity-65 text-xm">${messageObj.sender.email?}</small>
              <p className="text-sm leading-4.5 pt-1 font-sans">${messageObj.message}</p>
            </div>`;

    messageBox.appendChild(messageBox.lastChild);
  };

  return (
    <main className="h-screen w-screen flex">
      <section className="left h-full min-w-72 bg-red-200 flex flex-col relative">
        <header className="w-full p-2 flex justify-between bg-blue-200 items-center">
          <button
            className="flex gap-2 items-center cursor-pointer hover:scale-105 transition-all duration-500"
            onClick={() => setIsUserModalOpen(true)}
          >
            <IoIosPersonAdd className="text-sx" />
            <p className="text-xs font-bold">Add colaborater</p>
          </button>
          <button
            onClick={() => setIsSidePanelOpen(!isSidePanelOpen)}
            className="p-2 rounded-full shadow-md cursor-pointer"
          >
            <HiUserGroup className="text-2xl hover:scale-105 transition-all duration-300" />
          </button>
        </header>

        {/* Conversation area */}
        <div className="coverstation-area flex-grow flex flex-col">
          <div
            className="message-box flex-grow flex flex-col gap-2 p-1"
            ref={messageBox}
          >
            <div className="incomming-messages flex flex-col p-2 bg-fuchsia-200 w-fit rounded-md max-w-56">
              <small className="opacity-65 text-xm"><EMAIL></small>
              <p className="text-sm leading-4.5 pt-1 font-sans">
                Lorem ipsum dolor sit, amet consectetur adipisicing elit.
                Incidunt, provident!
              </p>
            </div>
            <div className="incomming-messages flex flex-col p-2 bg-fuchsia-200 w-fit rounded-md max-w-56 ml-auto">
              <small className="opacity-65 text-xm"><EMAIL></small>
              <p className="text-sm leading-4.5 pt-1 font-sans">Hello</p>
            </div>
          </div>
          <div className="inputfield flex items-center">
            <input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  send();
                }
              }}
              type="text"
              placeholder="Enter your message"
              className="p-2 px-2 border-none outline-none bg-blue-100 rounded-sm flex-grow"
            />
            <button
              onClick={send}
              className="cursor-pointer px-3 hover:scale-125 duration-300 rounded-full"
            >
              <IoIosSend className="text-2xl" />
            </button>
          </div>
        </div>

        <div
          className={`flex flex-col side-pannel w-full h-full absolute bg-blue-100 transition-all duration-300 ${
            isSidePanelOpen ? "translate-x-0" : "-translate-x-full"
          } top-0 z-10`}
        >
          <header className="flex justify-between p-2 px-3 bg-amber-200 items-center">
            <h1 className="font-bold text-sm cursor-pointer w-full p-2">
              Collaborates
            </h1>
            <button
              onClick={() => setIsSidePanelOpen(!isSidePanelOpen)}
              className="p-2 rounded-full shadow-md cursor-pointer"
            >
              <GiCrossMark className="text-xl" />
            </button>
          </header>
          {/* Users */}
          <div className="users flex flex-col p-5">
            {project.user.map((user) => (
              <div
                key={user._id}
                className="flex gap-2 items-center p-1 px-2 rounded-2xl hover:bg-[#6588a91c]"
              >
                <div className="bg-[#a5a2a2] rounded-full p-2 w-fit h-fit">
                  <FaRegUser />
                </div>
                <h1 className="font-medium text-sm cursor-pointer w-full p-2">
                  {user.email}
                </h1>
              </div>
            ))}
          </div>
        </div>

        {/* User Selection Modal */}
        {isUserModalOpen && (
          <div className="fixed inset-0 overflow-y-auto z-50">
            <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
              {/* Background overlay */}
              <div
                className="fixed inset-0 transition-opacity"
                aria-hidden="true"
                onClick={() => setIsUserModalOpen(false)}
              >
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>

              {/* Modal panel */}
              <div
                className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg w-full max-w-md relative z-50"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Select Users to Add
                    </h3>
                    <button
                      onClick={() => setIsUserModalOpen(false)}
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <GiCrossMark className="h-5 w-5" />
                    </button>
                  </div>

                  {error && (
                    <div className="mb-4 text-sm text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </div>
                  )}

                  {loading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    </div>
                  ) : (
                    <div className="max-h-60 overflow-y-auto">
                      {users.length === 0 ? (
                        <p className="text-center text-gray-500 py-4">
                          No users available
                        </p>
                      ) : (
                        <ul className="divide-y divide-gray-200">
                          {users.map((user) => (
                            <li
                              key={user._id}
                              className={`flex items-center py-3 px-2 cursor-pointer hover:bg-gray-50 rounded-md ${
                                selectedUsers.includes(user._id)
                                  ? "bg-blue-50"
                                  : ""
                              }`}
                              onClick={() => toggleUserSelection(user._id)}
                            >
                              <div className="flex-shrink-0 bg-gray-300 rounded-full p-2">
                                <FaRegUser className="text-gray-600" />
                              </div>
                              <div className="ml-3 flex-grow">
                                <p className="text-sm font-medium text-gray-900">
                                  {user.email}
                                </p>
                              </div>
                              {selectedUsers.includes(user._id) && (
                                <IoMdCheckmarkCircle className="h-5 w-5 text-green-500" />
                              )}
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  )}

                  <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      onClick={handleAddUsers}
                      disabled={loading || selectedUsers.length === 0}
                      className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm disabled:bg-indigo-300"
                    >
                      Add Selected Users
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsUserModalOpen(false)}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </section>
    </main>
  );
};

export default Project;
