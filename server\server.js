const http = require("http");
const app = require("./app");
require("dotenv").config();
const jwt = require("jsonwebtoken");
const { default: mongoose } = require("mongoose");
const projectModel = require("./models/project.model");

const port = process.env.PORT || 3000;

const server = http.createServer(app);
const io = require("socket.io")(server, {
  cors: {
    origin: "*",
  },
});

io.use(async (socket, next) => {
  try {
    const token =
      socket.handshake.auth.token ||
      socket.handshake.headers?.authorization?.split(" ")[1];
    const projectId = socket.handshake.query.projectId;

    if (!mongoose.Types.ObjectId.isValid(projectId)) {
      return next(new Error("Invalid project ID"));
    }

    socket.project = await projectModel.findById(projectId);

    if (!token) {
      return next(new Error("Authentication error"));
    }
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    if (!decoded) {
      return next(new Error("Authentication error"));
    }
    socket.user = decoded;
    next();
  } catch (error) {
    console.log(error);
    return next(new Error("Authentication error"));
  }
});

io.on("connection", (socket) => {
  socket.roomId = socket.project._id.toString();
  console.log("New client connected");

  socket.join(socket.roomId);

  socket.on("project-message", async (data) => {
    console.log(data);
    socket.broadcast.to(socket.roomId).emit("project-message", data);
  });

  socket.on("event", (data) => {});
  socket.on("disconnect", () => {});
});

server.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});
